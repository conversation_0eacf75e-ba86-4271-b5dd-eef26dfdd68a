import traceback

import requests

from models.bybit_order import BybitOrder


# Other imports are done within methods to avoid circular imports


class Bybit:
    API_URL = "http://128.199.252.188:8000"
    API_TOKEN = "yoursecret123"
    HEADERS = {"x-api-token": API_TOKEN}

    @staticmethod
    def getSession():
        raise NotImplementedError("Remote proxy does not use getSession().")

    @staticmethod
    def getExecutions():
        r = requests.get(f"{Bybit.API_URL}/bybit/executions", headers=Bybit.HEADERS)
        return r.json()

    @staticmethod
    def getPrice(ticker):
        r = requests.get(f"{Bybit.API_URL}/bybit/price", params={"ticker": ticker}, headers=Bybit.HEADERS)
        return float(r.json())

    @staticmethod
    def get_current_open_positions(category: str = "linear", settleCoin: str = None):
        """
        Retrieves open positions from Bybit and creates Trade objects
        representing the current open positions.

        Args:
            category: The product category (e.g., "linear", "inverse", "spot"). Default is "linear".
            settleCoin: The settlement currency (e.g., "USDT", "USD", "BTC"). Optional.

        Returns:
            list: A list of Trade objects representing open positions
        """
        from models.bybit_position import BybitPosition
        from models.trade import Trade

        # Prepare parameters for the API call
        params = {"category": category}
        if settleCoin:
            params["settleCoin"] = settleCoin

        # Get positions data from API
        r = requests.get(f"{Bybit.API_URL}/bybit/positions", params=params, headers=Bybit.HEADERS)
        positions_data = r.json()

        # Filter and convert to BybitPosition objects
        # Check for both "qty" and "size" fields to handle different API response formats
        positions = []
        for p in positions_data:
            # Check if position has size/qty > 0
            position_size = float(p.get("size", p.get("qty", 0)))
            if position_size > 0:
                try:
                    position = BybitPosition(p)
                    positions.append(position)
                except Exception as e:
                    traceback.print_exc()
                    print(f"Error creating BybitPosition from data: {e}")
                    print(f"Position data: {p}")
                    # Continue processing other positions even if one fails

        # Convert positions directly to trades
        trades = []
        for position in positions:
            try:
                # Create a Trade object directly from Bybit Position since we don't have opening order data
                trade = Trade.from_bybit_position(position)
                trades.append(trade)
            except Exception as e:
                # import logging
                traceback.print_exc()
                print(f"Error processing position {position.symbol}: {e}")
                # Continue processing other positions even if one fails

        # Log summary
        print(f"Processed {len(positions)} positions into {len(trades)} trades")
        return trades

    @staticmethod
    def getOrders(start_ms=None, end_ms=None):
        params = {}
        if start_ms: params["startTime"] = int(start_ms)
        if end_ms: params["endTime"] = int(end_ms)

        r = requests.get(f"{Bybit.API_URL}/bybit/orders", params=params, headers=Bybit.HEADERS)
        orders_list = [BybitOrder(order) for order in r.json()]
        return orders_list

    @staticmethod
    def getAccountBalance():
        """
        Gets the account balance from Bybit as a formatted string.

        Returns:
            str: Formatted account balance
        """
        r = requests.get(f"{Bybit.API_URL}/bybit/balance", headers=Bybit.HEADERS)
        return f"{float(r.json()['equity']):.4f}"

    @staticmethod
    def getAccountBalanceDecimal():
        """
        Gets the account balance from Bybit as a Decimal value.

        Returns:
            Decimal: Account balance as a Decimal
        """
        from decimal import Decimal
        r = requests.get(f"{Bybit.API_URL}/bybit/balance", headers=Bybit.HEADERS)
        return Decimal(str(r.json()['equity']))

    @staticmethod
    def get_bybit_orders(category: str = "linear", settleCoin: str = None):
        """
        Retrieves orders from Bybit and current open positions.
        
        Args:
            category: The product category (e.g., "linear", "inverse", "spot"). Default is "linear".
            settleCoin: The settlement currency (e.g., "USDT", "USD", "BTC"). Optional.
        
        Returns:
            tuple: (current_open_positions, orders, first_import) - Lists of current open positions and orders,
            and a boolean indicating if it's the first import
        """
        from datetime import datetime, timedelta
        import time
        from models.order import Order
        from models.trade import Exchange
        from models.trade import TradeStatus
        from trades_db import TradesDB
        import helper

        last_update = helper.date_to_ms(TradesDB.get_bybit_last_order_time())
        current_open_positions = []
        if last_update:
            now_ms = int(time.time() * 1000)
            orders = Bybit.getOrders(start_ms=last_update + 1, end_ms=now_ms)

            # For non-first imports, we get open positions from the DB since we recreate oldest - newest now
            saved_open_trades = TradesDB.get_trades(exchange=Exchange.BYBIT)
            for trade in saved_open_trades:
                if trade.status == TradeStatus.OPEN:
                    current_open_positions.append(trade)
            # Sort current_open_positions by oldest first
            current_open_positions.sort(key=lambda t: t.timeOpen, reverse=False)
        else:
            # 2 years ago in milliseconds
            two_years_ago = datetime.utcnow() - timedelta(days=5)  # 365 * 2
            two_years_ms = int(two_years_ago.timestamp() * 1000)
            now_ms = int(time.time() * 1000)

            start_dt = datetime.utcfromtimestamp(two_years_ms / 1000)
            end_dt = datetime.utcfromtimestamp(now_ms / 1000)

            start_ms = int(start_dt.timestamp() * 1000)
            end_ms = int(end_dt.timestamp() * 1000)

            # Calculates the first 7 day chunk
            window_end_ms = min(start_ms + (7 * 24 * 60 * 60 * 1000), end_ms)
            Bybit.print_time_diff(start_ms, window_end_ms)
            orders = Bybit.getOrders(start_ms=start_ms, end_ms=now_ms)

            # For first import scenario we get live open positions, no open positions in DB (duh), also
            # Re-create trades first run is newest - oldest
            current_open_positions = Bybit.get_current_open_positions(category=category, settleCoin=settleCoin)

            # Sort current_open_positions by newest first
            current_open_positions.sort(key=lambda t: t.timeOpen, reverse=True)

        # Get the set of already processed order IDs
        processed_order_ids = set(TradesDB.getBybitOrderIds())

        # Filter out orders that have already been processed
        orders = [order for order in orders if order.bybit_order_id not in processed_order_ids]

        # Convert and return the new orders
        orders = [Order.from_bybit_order(order) for order in orders]

        # Sort orders chronologically by filled_date (or created_date if filled_date is None)
        # This ensures we process the oldest orders first for proper trade reconstruction
        def get_order_date(order):
            return order.filled_date if order.filled_date else order.created_date

        if last_update:
            sorted_orders = sorted(orders, key=get_order_date, reverse=False)
        else:
            sorted_orders = sorted(orders, key=get_order_date, reverse=True)
        return current_open_positions, sorted_orders, True if last_update is None else False

    @staticmethod
    def print_time_diff(start_ms, end_ms):
        """
        Prints the time difference between two timestamps in a human-readable format.
        
        Args:
            start_ms: Start time in milliseconds
            end_ms: End time in milliseconds
        """
        diff_ms = end_ms - start_ms

        days = diff_ms // (24 * 60 * 60 * 1000)
        diff_ms %= (24 * 60 * 60 * 1000)

        hours = diff_ms // (60 * 60 * 1000)
        diff_ms %= (60 * 60 * 1000)

        minutes = diff_ms // (60 * 1000)
        diff_ms %= (60 * 1000)

        seconds = diff_ms // 1000
        ms = diff_ms % 1000

        print(f"Difference: {days}d {hours}h {minutes}m {seconds}s {ms}ms")

